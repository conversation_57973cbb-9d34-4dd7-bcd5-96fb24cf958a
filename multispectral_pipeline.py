"""
multispectral_pipeline.py

Core pipeline for computing multispectral coherence using spectral decomposition
and eigenstructure-based coherence calculation.

This pipeline is designed to work with any time-frequency transform that follows
the expected interface.
"""

import numpy as np
from tqdm import tqdm
from typing import Callable, Dict, Tuple, Optional, List, Union

# Import coherence functions
from coherence_lib import gersztenkorn, multispectral_gersztenkorn


class MultispectralCoherencePipeline:
    """
    A flexible pipeline for computing multispectral coherence.
    
    This class accepts time-frequency transform functions as parameters,
    allowing it to work with different spectral decomposition methods.
    """
    
    def __init__(self, 
                 transform_2d: Optional[Callable] = None,
                 transform_3d: Optional[Callable] = None):
        """
        Initialize the pipeline with time-frequency transform functions.
        
        Parameters
        ----------
        transform_2d : Callable, optional
            Function for 2D time-frequency transform with signature:
            (seismic_data, **kwargs) -> (spectral_voices, frequencies, t_out)
        transform_3d : Callable, optional
            Function for 3D time-frequency transform with signature:
            (seismic_data, **kwargs) -> (spectral_voices, frequencies, t_out)
        """
        self.transform_2d = transform_2d
        self.transform_3d = transform_3d
        
    def compute(self,
                seismic_data: np.ndarray,
                window_size: Tuple[int, int, int] = (5, 5, 11),
                compare_broadband: bool = False,
                transform_params: Optional[Dict] = None,
                progress_bar: bool = True) -> Dict[str, np.ndarray]:
        """
        Compute multispectral coherence for seismic data.
        
        Parameters
        ----------
        seismic_data : numpy.ndarray
            2D (samples, traces) or 3D (samples, ilines, xlines) seismic data.
        window_size : tuple, optional
            Size of analysis window (n_traces_x, n_traces_y, n_samples) for 3D
            or (n_traces, 1, n_samples) for 2D. Default is (5, 5, 11).
        compare_broadband : bool, optional
            If True, also computes broadband coherence for comparison.
        transform_params : dict, optional
            Parameters to pass to the time-frequency transform function.
        progress_bar : bool, optional
            Whether to show progress bar during computation.
            
        Returns
        -------
        dict
            Dictionary containing:
            - 'multispectral': Multispectral coherence volume
            - 'frequencies': Array of frequencies used
            - 't': Time/depth array
            - 'broadband': Broadband coherence volume (if compare_broadband=True)
        """
        
        if transform_params is None:
            transform_params = {}
            
        # Apply spectral decomposition
        spectral_data = self._apply_spectral_decomposition(
            seismic_data, transform_params
        )
        
        spectral_voices = spectral_data['spectral_voices']
        frequencies = spectral_data['frequencies']
        t_out = spectral_data['t']
        
        # Get dimensions
        dims = self._get_dimensions(seismic_data, spectral_voices)
        
        # Initialize output volumes
        multispectral_coherence = np.zeros(dims['output_shape'])
        if compare_broadband:
            broadband_coherence = np.zeros(dims['output_shape'])
        
        # Compute coherence
        print("Computing multispectral coherence...")
        coherence_results = self._compute_coherence_volume(
            seismic_data=seismic_data,
            spectral_voices=spectral_voices,
            window_size=window_size,
            dims=dims,
            compute_broadband=compare_broadband,
            progress_bar=progress_bar
        )
        
        # Prepare results
        results = {
            'multispectral': coherence_results['multispectral'],
            'frequencies': frequencies,
            't': t_out
        }
        
        if compare_broadband:
            results['broadband'] = coherence_results['broadband']
        
        # Remove dummy dimension for 2D case
        if seismic_data.ndim == 2:
            results['multispectral'] = np.squeeze(results['multispectral'])
            if compare_broadband:
                results['broadband'] = np.squeeze(results['broadband'])
        
        print("Multispectral coherence computation complete!")
        
        return results
    
    def _apply_spectral_decomposition(self, 
                                      seismic_data: np.ndarray,
                                      transform_params: Dict) -> Dict[str, np.ndarray]:
        """Apply the appropriate time-frequency transform."""
        
        if seismic_data.ndim == 2:
            if self.transform_2d is None:
                raise ValueError("No 2D transform function provided")
            
            print("Processing 2D seismic section...")
            spectral_voices, frequencies, t_out = self.transform_2d(
                seismic_data, **transform_params
            )
            
            # Ensure correct output format: (freqs, samples, traces)
            if spectral_voices.shape[2] == len(frequencies):
                spectral_voices = np.transpose(spectral_voices, (2, 0, 1))
            
            # Add dummy dimension for 2D case
            spectral_voices = spectral_voices[:, :, np.newaxis, :]
            
        elif seismic_data.ndim == 3:
            if self.transform_3d is None:
                raise ValueError("No 3D transform function provided")
            
            print("Processing 3D seismic volume...")
            spectral_voices, frequencies, t_out = self.transform_3d(
                seismic_data, **transform_params
            )
            
            # Ensure correct output format: (freqs, samples, ilines, xlines)
            if spectral_voices.shape[3] == len(frequencies):
                spectral_voices = np.transpose(spectral_voices, (3, 0, 1, 2))
                
        else:
            raise ValueError("Input must be 2D or 3D seismic data")
        
        n_freqs = len(frequencies)
        print(f"Spectral decomposition complete. {n_freqs} frequency bands extracted.")
        print(f"Frequency range: {frequencies[0]:.1f} - {frequencies[-1]:.1f} Hz")
        
        return {
            'spectral_voices': spectral_voices,
            'frequencies': frequencies,
            't': t_out
        }
    
    def _get_dimensions(self, 
                        seismic_data: np.ndarray,
                        spectral_voices: np.ndarray) -> Dict[str, Union[int, Tuple]]:
        """Extract dimensions from input data."""
        
        if seismic_data.ndim == 2:
            n_samples, n_traces = seismic_data.shape
            n_ilines, n_xlines = 1, n_traces
        else:
            n_samples, n_ilines, n_xlines = seismic_data.shape
            
        n_freqs = spectral_voices.shape[0]
        
        return {
            'n_samples': n_samples,
            'n_ilines': n_ilines,
            'n_xlines': n_xlines,
            'n_freqs': n_freqs,
            'output_shape': (n_samples, n_ilines, n_xlines),
            'is_2d': seismic_data.ndim == 2
        }
    
    def _compute_coherence_volume(self,
                                  seismic_data: np.ndarray,
                                  spectral_voices: np.ndarray,
                                  window_size: Tuple[int, int, int],
                                  dims: Dict,
                                  compute_broadband: bool,
                                  progress_bar: bool) -> Dict[str, np.ndarray]:
        """Compute coherence using sliding window approach."""
        
        # Initialize output arrays
        multispectral_coherence = np.zeros(dims['output_shape'])
        if compute_broadband:
            broadband_coherence = np.zeros(dims['output_shape'])
        else:
            broadband_coherence = None
            
        # Define half-window sizes
        if dims['is_2d']:
            half_win_x = window_size[0] // 2
            half_win_y = 0
            half_win_t = window_size[2] // 2
        else:
            half_win_x = window_size[0] // 2
            half_win_y = window_size[1] // 2
            half_win_t = window_size[2] // 2
        
        # Setup progress bar
        total_points = dims['n_samples'] * dims['n_ilines'] * dims['n_xlines']
        pbar = tqdm(total=total_points, desc="Processing") if progress_bar else None
        
        # Main computation loop
        for i in range(dims['n_samples']):
            for j in range(dims['n_ilines']):
                for k in range(dims['n_xlines']):
                    # Extract windows
                    windows = self._extract_windows(
                        i, j, k,
                        seismic_data, spectral_voices,
                        half_win_t, half_win_x, half_win_y,
                        dims
                    )
                    
                    # Compute multispectral coherence
                    multispectral_coherence[i, j, k] = multispectral_gersztenkorn(
                        windows['spectral']
                    )
                    
                    # Compute broadband coherence if requested
                    if compute_broadband and windows['broadband'] is not None:
                        broadband_coherence[i, j, k] = gersztenkorn(
                            windows['broadband']
                        )
                    
                    if pbar is not None:
                        pbar.update(1)
        
        if pbar is not None:
            pbar.close()
            
        results = {'multispectral': multispectral_coherence}
        if compute_broadband:
            results['broadband'] = broadband_coherence
            
        return results
    
    def _extract_windows(self, i: int, j: int, k: int,
                         seismic_data: np.ndarray,
                         spectral_voices: np.ndarray,
                         half_win_t: int, half_win_x: int, half_win_y: int,
                         dims: Dict) -> Dict[str, Union[List[np.ndarray], np.ndarray]]:
        """Extract analysis windows for coherence computation."""
        
        # Time window bounds
        t_start = max(0, i - half_win_t)
        t_end = min(dims['n_samples'], i + half_win_t + 1)
        
        voice_windows = []
        
        if dims['is_2d']:
            # 2D case
            x_start = max(0, k - half_win_x)
            x_end = min(dims['n_xlines'], k + half_win_x + 1)
            
            # Collect windows from all spectral voices
            for freq_idx in range(dims['n_freqs']):
                window = spectral_voices[freq_idx, t_start:t_end, 0, x_start:x_end]
                window = window.T[:, np.newaxis, :]  # Reshape to 3D
                voice_windows.append(window)
            
            # Extract broadband window
            broadband_window = seismic_data[t_start:t_end, x_start:x_end]
            broadband_window = broadband_window.T[:, np.newaxis, :]
            
        else:
            # 3D case
            y_start = max(0, j - half_win_y)
            y_end = min(dims['n_ilines'], j + half_win_y + 1)
            x_start = max(0, k - half_win_x)
            x_end = min(dims['n_xlines'], k + half_win_x + 1)
            
            # Collect windows from all spectral voices
            for freq_idx in range(dims['n_freqs']):
                window = spectral_voices[freq_idx, t_start:t_end, y_start:y_end, x_start:x_end]
                window = np.transpose(window, (2, 1, 0))  # To (x, y, t) format
                voice_windows.append(window)
            
            # Extract broadband window
            broadband_window = seismic_data[t_start:t_end, y_start:y_end, x_start:x_end]
            broadband_window = np.transpose(broadband_window, (2, 1, 0))
            
        return {
            'spectral': voice_windows,
            'broadband': broadband_window
        }


def run_multispectral_coherence(seismic_data: np.ndarray,
                                transform_2d: Optional[Callable] = None,
                                transform_3d: Optional[Callable] = None,
                                window_size: Tuple[int, int, int] = (5, 5, 11),
                                compare_broadband: bool = False,
                                transform_params: Optional[Dict] = None,
                                progress_bar: bool = True) -> Dict[str, np.ndarray]:
    """
    Convenience function to run multispectral coherence analysis.
    
    This function creates a pipeline instance and runs the computation.
    
    Parameters
    ----------
    seismic_data : numpy.ndarray
        2D or 3D seismic data
    transform_2d : Callable, optional
        2D time-frequency transform function
    transform_3d : Callable, optional
        3D time-frequency transform function
    window_size : tuple, optional
        Analysis window size
    compare_broadband : bool, optional
        Whether to compute broadband coherence for comparison
    transform_params : dict, optional
        Parameters for the time-frequency transform
    progress_bar : bool, optional
        Whether to show progress bar
        
    Returns
    -------
    dict
        Results dictionary with coherence volumes and metadata
    """
    
    pipeline = MultispectralCoherencePipeline(
        transform_2d=transform_2d,
        transform_3d=transform_3d
    )
    
    return pipeline.compute(
        seismic_data=seismic_data,
        window_size=window_size,
        compare_broadband=compare_broadband,
        transform_params=transform_params,
        progress_bar=progress_bar
    )
