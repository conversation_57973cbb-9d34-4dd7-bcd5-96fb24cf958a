"""
example_usage.py

Example usage of the multispectral coherence pipeline with specific
time-frequency transforms.

This example demonstrates how to:
1. Import and configure time-frequency transforms
2. Create synthetic seismic data
3. Run the multispectral coherence pipeline
4. Visualize the results
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple

# Import the Borga transform functions
from fborga_2d_3d_gmn import fborga_2d, fborga_3d

# Import the pipeline
from multispectral_pipeline import run_multispectral_coherence, MultispectralCoherencePipeline


def create_synthetic_3d_data(n_samples: int = 100, 
                           n_ilines: int = 50, 
                           n_xlines: int = 50, 
                           dt: float = 0.004) -> Tuple[np.ndarray, np.ndarray]:
    """
    Create synthetic 3D seismic data with a fault for testing.
    
    Parameters
    ----------
    n_samples : int
        Number of time samples
    n_ilines : int
        Number of inlines
    n_xlines : int
        Number of crosslines
    dt : float
        Sample interval in seconds
        
    Returns
    -------
    data : numpy.ndarray
        Synthetic seismic data
    t : numpy.ndarray
        Time axis
    """
    t = np.arange(n_samples) * dt
    
    # Create base reflectivity model
    data = np.zeros((n_samples, n_ilines, n_xlines))
    
    # Add some reflectors
    reflector_times = [20, 40, 60, 80]
    for ref_t in reflector_times:
        # Add some structural variation
        dip = 0.1 * (np.arange(n_xlines) - n_xlines/2)
        for i in range(n_ilines):
            for j in range(n_xlines):
                t_idx = int(ref_t + dip[j])
                if 0 <= t_idx < n_samples:
                    data[t_idx, i, j] = np.random.randn()
    
    # Add a fault at xline 25
    fault_location = 25
    fault_throw = 10  # samples
    data[:, :, fault_location:] = np.roll(data[:, :, fault_location:], fault_throw, axis=0)
    
    # Add noise
    noise_level = 0.1
    data += noise_level * np.random.randn(*data.shape)
    
    # Apply a Ricker wavelet
    from scipy.signal import ricker
    wavelet = ricker(15, 4)
    for i in range(n_ilines):
        for j in range(n_xlines):
            data[:, i, j] = np.convolve(data[:, i, j], wavelet, mode='same')
    
    return data, t


def create_synthetic_2d_data(n_samples: int = 200, 
                           n_traces: int = 100, 
                           dt: float = 0.002) -> Tuple[np.ndarray, np.ndarray]:
    """
    Create synthetic 2D seismic data with faults and channels.
    """
    t = np.arange(n_samples) * dt
    
    # Create base reflectivity model
    data = np.zeros((n_samples, n_traces))
    
    # Add horizontal reflectors with slight dip
    reflector_times = [30, 60, 90, 120, 150]
    for ref_t in reflector_times:
        dip = 0.05 * (np.arange(n_traces) - n_traces/2)
        for j in range(n_traces):
            t_idx = int(ref_t + dip[j])
            if 0 <= t_idx < n_samples:
                data[t_idx, j] = np.random.randn()
    
    # Add a fault
    fault_location = 50
    fault_throw = 15
    data[:, fault_location:] = np.roll(data[:, fault_location:], fault_throw, axis=0)
    
    # Add a channel
    channel_center = 75
    channel_width = 10
    channel_depth = 100
    channel_traces = range(channel_center - channel_width//2, 
                          channel_center + channel_width//2)
    for trace in channel_traces:
        if 0 <= trace < n_traces:
            data[channel_depth:channel_depth+5, trace] = 0.5 * np.random.randn(5)
    
    # Add noise
    data += 0.1 * np.random.randn(*data.shape)
    
    # Apply wavelet
    from scipy.signal import ricker
    wavelet = ricker(15, 4)
    for j in range(n_traces):
        data[:, j] = np.convolve(data[:, j], wavelet, mode='same')
    
    return data, t


def visualize_results(results, seismic_data, slice_idx=None):
    """
    Visualize coherence results for both 2D and 3D data.
    """
    if seismic_data.ndim == 3:
        # For 3D data, show a time slice
        if slice_idx is None:
            slice_idx = seismic_data.shape[0] // 2
            
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Seismic amplitude
        im1 = axes[0].imshow(seismic_data[slice_idx, :, :], aspect='auto', cmap='seismic')
        axes[0].set_title(f'Seismic Amplitude (t={slice_idx})')
        axes[0].set_xlabel('Crossline')
        axes[0].set_ylabel('Inline')
        plt.colorbar(im1, ax=axes[0], fraction=0.046, pad=0.04)
        
        # Multispectral coherence
        im2 = axes[1].imshow(results['multispectral'][slice_idx, :, :], 
                            aspect='auto', cmap='gray_r', vmin=0, vmax=1)
        axes[1].set_title(f'Multispectral Coherence (t={slice_idx})')
        axes[1].set_xlabel('Crossline')
        axes[1].set_ylabel('Inline')
        plt.colorbar(im2, ax=axes[1], fraction=0.046, pad=0.04)
        
        # Broadband coherence (if available)
        if 'broadband' in results:
            im3 = axes[2].imshow(results['broadband'][slice_idx, :, :], 
                                aspect='auto', cmap='gray_r', vmin=0, vmax=1)
            axes[2].set_title(f'Broadband Coherence (t={slice_idx})')
            axes[2].set_xlabel('Crossline')
            axes[2].set_ylabel('Inline')
            plt.colorbar(im3, ax=axes[2], fraction=0.046, pad=0.04)
        else:
            axes[2].text(0.5, 0.5, 'Broadband coherence\nnot computed', 
                        ha='center', va='center', transform=axes[2].transAxes)
            axes[2].set_axis_off()
            
    else:  # 2D data
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        
        # Seismic amplitude
        im1 = axes[0].imshow(seismic_data.T, aspect='auto', cmap='seismic',
                            extent=[0, seismic_data.shape[1], 
                                   results['t'][-1], results['t'][0]])
        axes[0].set_title('Seismic Amplitude')
        axes[0].set_xlabel('Trace')
        axes[0].set_ylabel('Time (s)')
        plt.colorbar(im1, ax=axes[0], fraction=0.046, pad=0.04)
        
        # Multispectral coherence
        im2 = axes[1].imshow(results['multispectral'].T, aspect='auto', 
                            cmap='gray_r', vmin=0, vmax=1,
                            extent=[0, seismic_data.shape[1], 
                                   results['t'][-1], results['t'][0]])
        axes[1].set_title('Multispectral Coherence')
        axes[1].set_xlabel('Trace')
        axes[1].set_ylabel('Time (s)')
        plt.colorbar(im2, ax=axes[1], fraction=0.046, pad=0.04)
        
        # Broadband coherence (if available)
        if 'broadband' in results:
            im3 = axes[2].imshow(results['broadband'].T, aspect='auto', 
                                cmap='gray_r', vmin=0, vmax=1,
                                extent=[0, seismic_data.shape[1], 
                                       results['t'][-1], results['t'][0]])
            axes[2].set_title('Broadband Coherence')
            axes[2].set_xlabel('Trace')
            axes[2].set_ylabel('Time (s)')
            plt.colorbar(im3, ax=axes[2], fraction=0.046, pad=0.04)
    
    plt.tight_layout()
    plt.show()


def example_3d_borga():
    """Example using 3D data with Borga transform."""
    
    print("=" * 60)
    print("3D Multispectral Coherence Example with Borga Transform")
    print("=" * 60)
    
    # Create synthetic 3D seismic data
    print("\nCreating synthetic 3D seismic data...")
    seismic_data, t = create_synthetic_3d_data(
        n_samples=100, 
        n_ilines=50, 
        n_xlines=50, 
        dt=0.004
    )
    
    # Define Borga transform parameters
    borga_params = {
        't': t,
        'fwidth': 10.0,  # Hz - width of Gaussian window
        'finc': 5.0,     # Hz - frequency increment
        'padflag': 1,
        'target_freqs': [10, 20, 30, 40, 50]  # Hz
    }
    
    # Run multispectral coherence
    print("\nRunning multispectral coherence analysis...")
    results = run_multispectral_coherence(
        seismic_data=seismic_data,
        transform_2d=fborga_2d,
        transform_3d=fborga_3d,
        window_size=(5, 5, 11),  # 5x5 traces, 11 time samples
        compare_broadband=True,
        transform_params=borga_params
    )
    
    # Visualize results
    print("\nVisualizing results...")
    visualize_results(results, seismic_data, slice_idx=50)
    
    # Print statistics
    print_statistics(results)


def example_2d_borga():
    """Example using 2D data with Borga transform."""
    
    print("\n" + "=" * 60)
    print("2D Multispectral Coherence Example with Borga Transform")
    print("=" * 60)
    
    # Create synthetic 2D seismic data
    print("\nCreating synthetic 2D seismic data...")
    seismic_data, t = create_synthetic_2d_data(
        n_samples=200, 
        n_traces=100, 
        dt=0.002
    )
    
    # Define Borga transform parameters
    borga_params = {
        't': t,
        'fwidth': 15.0,  # Hz
        'finc': 10.0,    # Hz
        'padflag': 1,
        'target_freqs': None  # Use all frequencies
    }
    
    # Run multispectral coherence
    print("\nRunning multispectral coherence analysis...")
    results = run_multispectral_coherence(
        seismic_data=seismic_data,
        transform_2d=fborga_2d,
        transform_3d=fborga_3d,
        window_size=(5, 1, 21),  # 5 traces, 21 time samples
        compare_broadband=True,
        transform_params=borga_params
    )
    
    # Visualize results
    print("\nVisualizing results...")
    visualize_results(results, seismic_data)
    
    # Print statistics
    print_statistics(results)


def example_custom_transform():
    """Example showing how to use a custom time-frequency transform."""
    
    print("\n" + "=" * 60)
    print("Example with Custom Time-Frequency Transform")
    print("=" * 60)
    
    # Define a simple custom transform (e.g., filter bank)
    def custom_transform_2d(data, freqs, dt, **kwargs):
        """
        Custom transform that creates a simple filter bank.
        This is just an example - replace with your own transform.
        """
        from scipy.signal import butter, filtfilt
        
        n_samples, n_traces = data.shape
        n_freqs = len(freqs)
        
        # Output array: (samples, traces, frequencies)
        output = np.zeros((n_samples, n_traces, n_freqs))
        
        for i, freq in enumerate(freqs):
            # Simple bandpass filter
            nyq = 0.5 / dt
            low = max(freq - 10, 1) / nyq
            high = min(freq + 10, nyq * 0.9) / nyq
            
            if low < high:
                b, a = butter(4, [low, high], btype='band')
                for j in range(n_traces):
                    output[:, j, i] = filtfilt(b, a, data[:, j])
        
        t_out = np.arange(n_samples) * dt
        return output, freqs, t_out
    
    # Create test data
    print("\nCreating synthetic 2D seismic data...")
    data, t = create_synthetic_2d_data(n_samples=150, n_traces=75, dt=0.002)
    
    # Define custom transform parameters
    custom_params = {
        'freqs': np.array([15, 25, 35, 45]),  # Target frequencies
        'dt': 0.002
    }
    
    # Run with custom transform
    print("\nRunning multispectral coherence with custom transform...")
    results = run_multispectral_coherence(
        seismic_data=data,
        transform_2d=custom_transform_2d,
        window_size=(5, 1, 15),
        compare_broadband=True,
        transform_params=custom_params
    )
    
    print("\nCustom transform example complete!")
    print(f"Used frequencies: {results['frequencies']} Hz")


def example_using_pipeline_class():
    """Example showing direct use of the pipeline class."""
    
    print("\n" + "=" * 60)
    print("Example Using Pipeline Class Directly")
    print("=" * 60)
    
    # Create pipeline instance
    pipeline = MultispectralCoherencePipeline(
        transform_2d=fborga_2d,
        transform_3d=fborga_3d
    )
    
    # Create data
    data, t = create_synthetic_3d_data(n_samples=80, n_ilines=40, n_xlines=40)
    
    # Define parameters
    params = {
        't': t,
        'fwidth': 12.0,
        'finc': 6.0,
        'target_freqs': [15, 25, 35, 45]
    }
    
    # Run pipeline
    print("\nRunning pipeline...")
    results = pipeline.compute(
        seismic_data=data,
        window_size=(3, 3, 9),
        transform_params=params,
        compare_broadband=True
    )
    
    print("Pipeline execution complete!")
    print_statistics(results)


def print_statistics(results):
    """Print coherence statistics."""
    print(f"\nFrequencies used: {results['frequencies']} Hz")
    
    print(f"\nMultispectral coherence statistics:")
    print(f"  Min: {results['multispectral'].min():.3f}")
    print(f"  Max: {results['multispectral'].max():.3f}")
    print(f"  Mean: {results['multispectral'].mean():.3f}")
    print(f"  Std: {results['multispectral'].std():.3f}")
    
    if 'broadband' in results:
        print(f"\nBroadband coherence statistics:")
        print(f"  Min: {results['broadband'].min():.3f}")
        print(f"  Max: {results['broadband'].max():.3f}")
        print(f"  Mean: {results['broadband'].mean():.3f}")
        print(f"  Std: {results['broadband'].std():.3f}")


if __name__ == '__main__':
    # Run different examples
    
    # 1. 3D example with Borga transform
    example_3d_borga()
    
    # 2. 2D example with Borga transform
    example_2d_borga()
    
    # 3. Example with custom transform
    example_custom_transform()
    
    # 4. Example using pipeline class directly
    example_using_pipeline_class()
