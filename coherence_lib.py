"""
coherence_lib.py

Library of coherence algorithms including broadband and multispectral approaches.

Based on:
- Gersztenkorn & Mar<PERSON> (1999): Eigenstructure-based coherence computations
- Marfurt (2017): Multispectral coherence
"""

import numpy as np
from scipy.ndimage import generic_filter
from scipy.ndimage import gaussian_filter1d


def moving_window(traces, func, window):
    """
    Helper function for multi-trace attribute generation.
    This function applies a 3D function func to process a
    region of shape `window` over a dataset `data`.
    """
    wrapped = lambda x: func(x.reshape(window))
    return generic_filter(traces, wrapped, window)


def marfurt(traces):
    """
    <PERSON><PERSON><PERSON>, K<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, 1999,
    Coherency calculations in the presence of structural dip: GEOPHYSICS, 64, 104-111.
    doi:10.1190/1.1444508
    """
    epsilon = 1e-12  # Small constant for numerical stability
    i, x, t = traces.shape
    traces = traces.reshape(-1, t)
    square_sums = np.sum(traces, axis=0)**2
    sum_squares = np.sum(traces**2, axis=0)
    
    # Add epsilon to prevent division by zero
    denominator = sum_squares.sum()
    if np.abs(denominator) < epsilon:
        return 0.0
        
    c = square_sums.sum() / (denominator + epsilon)
    
    # Add check for valid trace count
    trace_count = i * x
    if trace_count == 0:
        return 0.0
        
    return c / trace_count


def gersztenkorn(traces):
    """
    Broadband coherence using eigenstructure method.
    
    Gersztenkorn, A., and K. J. Marfurt, 1999, Eigenstructure‐based coherence
    computations as an aid to 3-D structural and stratigraphic mapping:
    GEOPHYSICS, 64, 1468-1479. doi:10.1190/1.1444651
    """
    epsilon = 1e-12  # Small constant for numerical stability
    
    # Stack traces in 3D traces into 2D array.
    traces = traces.reshape(-1, traces.shape[-1])
    
    # Skip if no traces
    if traces.size == 0:
        return 0.0
        
    # Calculate eigenvalues of covariance matrix.
    cov = traces.dot(traces.T)
    vals = np.linalg.eigvalsh(cov)
    
    # Handle empty or invalid eigenvalues
    if len(vals) == 0:
        return 0.0
        
    # Add epsilon to prevent division by zero
    denominator = vals.sum()
    if np.abs(denominator) < epsilon:
        return 0.0
        
    return vals.max() / (denominator + epsilon)


def multispectral_gersztenkorn(spectral_voices):
    """
    Multispectral coherence using eigenstructure method with covariance matrix summation.
    
    This implements the key innovation of multispectral coherence where individual
    covariance matrices from each spectral voice are summed before eigendecomposition.
    
    C_multispectral = C_1 + C_2 + ... + C_L
    
    Parameters
    ----------
    spectral_voices : list of numpy.ndarray
        List of 3D arrays, each representing a spectral voice window
        with shape (n_traces_x, n_traces_y, n_samples).
    
    Returns
    -------
    float
        Multispectral coherence value (0-1).
    """
    epsilon = 1e-12
    
    if not spectral_voices or len(spectral_voices) == 0:
        return 0.0
    
    # Initialize accumulated covariance matrix
    accumulated_cov = None
    
    # Sum covariance matrices from all spectral voices
    for voice in spectral_voices:
        # Reshape 3D traces to 2D array (traces x samples)
        voice_2d = voice.reshape(-1, voice.shape[-1])
        
        # Skip if no traces
        if voice_2d.size == 0:
            continue
            
        # Calculate covariance matrix for this spectral voice
        cov_l = voice_2d.dot(voice_2d.T)
        
        # Accumulate covariance matrices
        if accumulated_cov is None:
            accumulated_cov = cov_l
        else:
            accumulated_cov += cov_l
    
    # If no valid covariance matrix was computed
    if accumulated_cov is None:
        return 0.0
    
    # Calculate eigenvalues of the summed covariance matrix
    vals = np.linalg.eigvalsh(accumulated_cov)
    
    # Handle empty or invalid eigenvalues
    if len(vals) == 0:
        return 0.0
        
    # Compute coherence as ratio of largest eigenvalue to sum of all eigenvalues
    denominator = vals.sum()
    if np.abs(denominator) < epsilon:
        return 0.0
        
    return vals.max() / (denominator + epsilon)


def gradients(traces, sigma):
    grads = []
    for axis in range(3):
        grad = gaussian_filter1d(traces, sigma, axis=axis, order=1)
        grads.append(grad[..., np.newaxis])
    return np.concatenate(grads, axis=3)


def moving_window4d(grad, window, func):
    """Applies the given function *func* over a moving *window*, reducing 
    the input *grad* array from 4D to 3D."""
    # Pad in the spatial dimensions, but leave the gradient dimension unpadded.
    half_window = [(x // 2, x // 2) for x in window] + [(0, 0)]
    padded = np.pad(grad, half_window, mode='reflect')
    
    out = np.empty(grad.shape[:3], dtype=float)
    for i, j, k in np.ndindex(out.shape):
        region = padded[i:i+window[0], j:j+window[1], k:k+window[2], :]
        out[i,j,k] = func(region)
    return out


def gst_calc(region):
    region = region.reshape(-1, 3)
    gst = region.T.dot(region)
    eigs = np.sort(np.linalg.eigvalsh(gst))[::-1]
    # Add small epsilon to denominator to prevent division by zero
    epsilon = 1e-10
    denominator = eigs[0] + eigs[1]
    if np.abs(denominator) < epsilon:
        return 0.0
    return (eigs[0] - eigs[1]) / denominator


def gst_discontinuity(seismic, window, sigma=1):
    """
    Gradient structure tensor discontinuity.
    """
    grad = gradients(seismic, sigma)
    return moving_window4d(grad, window, gst_calc)


def discontinuity(traces, duration, dt, step_out=1, kind='gst', sigma=1):
    """
    Compute discontinuity for a seismic section using one of various methods.

    Expects time or depth to be in the last axis of a 2D or 3D input.

    :param traces: A 2D or 3D NumPy array arranged as (cdp, twt) or
        (iline, xline, twt).
    :param duration: The length in seconds of the window trace kernel
        used to calculate the discontinuity.
    :keyword dt (default=1): The sample interval of the traces in sec.
        (eg. 0.001, 0.002, ...). Will default to one, allowing
        duration to be given in samples.
    :keyword step_out (default=1):
        The number of adjacent traces to the kernel to compute discontinuity over.
    :keyword kind (default='gst'):
        The method to use for the computation. Can be "marfurt", "gersztenkorn"
        or "gst" (gradient structure tensor).
    :keyword sigma (default=1):
        The width of the Gaussian function used to compute gradients.
    """
    if traces.ndim == 2:
        traces = traces[:, None, :]
        window = 2*step_out+1, 1, int(duration / dt)
    elif traces.ndim == 3:
        window = 2*step_out+1, int(duration / dt), 2*step_out+1
    else:
        raise NotImplementedError("Expected 2D or 3D seismic data.")

    methods = {
        "marfurt": moving_window(traces, marfurt, window),
        "gersztenkorn": moving_window(traces, gersztenkorn, window),
        "gst": gst_discontinuity(traces, window, sigma)
    }

    return np.squeeze(methods[kind])
